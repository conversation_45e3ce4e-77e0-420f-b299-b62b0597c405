# 🤝 Contributing to ProjectBuzz

Thank you for your interest in contributing to ProjectBuzz! We welcome contributions from developers of all skill levels.

## 🚀 Getting Started

1. **Fork the repository**
2. **Clone your fork**: `git clone https://github.com/YOUR_USERNAME/Project_buzzV1.git`
3. **Install dependencies**: `npm run install:all`
4. **Set up environment**: `npm run setup`
5. **Start development**: `npm run dev`

## 📋 How to Contribute

### 🐛 Bug Reports
- Use the GitHub issue tracker
- Include steps to reproduce
- Provide screenshots if applicable
- Mention your environment (OS, browser, Node.js version)

### ✨ Feature Requests
- Check existing issues first
- Describe the feature clearly
- Explain the use case and benefits
- Consider implementation complexity

### 💻 Code Contributions
- Follow the existing code style
- Write clear commit messages
- Add tests for new features
- Update documentation as needed

## 🔧 Development Guidelines

### **Code Style**
- Use TypeScript for type safety
- Follow ESLint and Prettier configurations
- Use meaningful variable and function names
- Add comments for complex logic

### **Commit Messages**
```
feat: add user authentication system
fix: resolve payment processing bug
docs: update API documentation
style: format code with prettier
refactor: optimize database queries
test: add unit tests for user service
```

### **Pull Request Process**
1. Create a feature branch: `git checkout -b feature/amazing-feature`
2. Make your changes and test thoroughly
3. Commit with descriptive messages
4. Push to your fork: `git push origin feature/amazing-feature`
5. Create a Pull Request with detailed description

## 🧪 Testing

- Run tests: `npm test`
- Check frontend: `cd frontend && npm test`
- Check backend: `cd backend && npm test`
- Ensure all tests pass before submitting PR

## 📞 Questions?

- Open an issue for questions
- Email: <EMAIL>
- Join our community discussions

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

**Happy Contributing! 🎉**
