# 🚀 ProjectBuzz - Digital Project Marketplace

**The premier digital marketplace for developers to buy and sell programming projects, source code, and innovative solutions.**

[![Live Demo](https://img.shields.io/badge/🌐_Live_Demo-projectbuzz.tech-blue?style=for-the-badge)](https://projectbuzz.tech)
[![GitHub](https://img.shields.io/badge/GitHub-Repository-black?style=for-the-badge&logo=github)](https://github.com/Aniruddha434/Project_buzzV1)
[![Vercel](https://img.shields.io/badge/Deployed_on-Vercel-black?style=for-the-badge&logo=vercel)](https://project-buzz-v.vercel.app)

![React](https://img.shields.io/badge/React-18.0-61DAFB?style=flat&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-3178C6?style=flat&logo=typescript)
![Node.js](https://img.shields.io/badge/Node.js-18.0-339933?style=flat&logo=node.js)
![MongoDB](https://img.shields.io/badge/MongoDB-Atlas-47A248?style=flat&logo=mongodb)
![Tailwind](https://img.shields.io/badge/Tailwind-CSS-06B6D4?style=flat&logo=tailwindcss)
![Razorpay](https://img.shields.io/badge/Razorpay-Payment-528FF0?style=flat&logo=razorpay)

![GitHub stars](https://img.shields.io/github/stars/Aniruddha434/Project_buzzV1?style=social)
![GitHub forks](https://img.shields.io/github/forks/Aniruddha434/Project_buzzV1?style=social)
![GitHub issues](https://img.shields.io/github/issues/Aniruddha434/Project_buzzV1)
![GitHub license](https://img.shields.io/github/license/Aniruddha434/Project_buzzV1)
![Code size](https://img.shields.io/github/languages/code-size/Aniruddha434/Project_buzzV1)
![Last commit](https://img.shields.io/github/last-commit/Aniruddha434/Project_buzzV1)

## 🌟 **Live Application**

**🔗 Visit ProjectBuzz**: [https://projectbuzz.tech](https://projectbuzz.tech)

## 📋 **Project Description**

ProjectBuzz is a comprehensive digital marketplace that connects developers worldwide. Whether you're looking to buy ready-made projects to accelerate your development process or sell your innovative coding solutions, ProjectBuzz provides a secure, user-friendly platform with integrated payment processing and project delivery systems.

### 🎯 **Key Highlights**

- **🛒 Buy & Sell Projects**: Complete marketplace for digital programming projects
- **💳 Secure Payments**: Integrated Razorpay payment gateway with wallet system
- **👥 Role-Based Access**: Separate dashboards for buyers, sellers, and administrators
- **📱 Responsive Design**: Modern UI with dark theme and mobile optimization
- **🔐 Secure Authentication**: MongoDB-based JWT authentication with OAuth integration
- **📊 Analytics Dashboard**: Comprehensive sales and purchase tracking
- **🚀 Production Ready**: Deployed on Vercel with MongoDB Atlas backend

## 🛠️ **Technology Stack**

### **Frontend**

- **React 18** with TypeScript
- **Tailwind CSS** + **shadcn/ui** components
- **Vite** for fast development and building
- **React Router** for navigation
- **Axios** for API communication
- **Three.js** for 3D animations

### **Backend**

- **Node.js** + **Express.js**
- **MongoDB Atlas** cloud database
- **JWT** authentication
- **Razorpay** payment integration
- **Multer** for file uploads
- **Nodemailer** for email notifications

### **Deployment & DevOps**

- **Frontend**: Vercel
- **Backend**: Render
- **Database**: MongoDB Atlas
- **Domain**: BigRock (projectbuzz.tech)
- **Version Control**: GitHub

## 🏗️ **Architecture Overview**

### Frontend (React + TypeScript)

- **Authentication**: MongoDB-based JWT authentication
- **UI Framework**: React with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **API Communication**: Axios with JWT token authentication
- **State Management**: React Context for authentication

### Backend (Node.js + Express)

- **Authentication**: JWT-based authentication with MongoDB
- **Database**: MongoDB Atlas (production) / Local MongoDB (development)
- **Payment Gateway**: Razorpay Payment Gateway
- **File Storage**: Local file storage with multer
- **API**: RESTful endpoints with validation
- **Security**: Helmet, CORS, input validation

### 🚀 **Key Features**

#### **🛒 Marketplace Features**

- ✅ **Project Catalog**: Browse and search programming projects by category, technology, and price
- ✅ **Advanced Filtering**: Filter by programming language, framework, complexity level
- ✅ **Project Details**: Comprehensive project descriptions with screenshots and demos
- ✅ **Multiple Images**: Support for up to 5 project images with hover cycling
- ✅ **Real-time Search**: Instant search with auto-suggestions

#### **💳 Payment & Commerce**

- ✅ **Razorpay Integration**: Secure payment processing with multiple payment methods
- ✅ **Wallet System**: Seller wallet management with automatic commission calculation
- ✅ **Instant Downloads**: Immediate project access after successful payment
- ✅ **Purchase History**: Complete transaction tracking for buyers and sellers
- ✅ **Payout Management**: Automated seller payouts with 3-4 day processing

#### **👥 User Management**

- ✅ **Role-Based Access**: Separate interfaces for buyers, sellers, and administrators
- ✅ **OAuth Integration**: Google and GitHub login (GitHub under development)
- ✅ **Profile Management**: Comprehensive user profiles with statistics
- ✅ **Seller Verification**: Enhanced registration process for sellers
- ✅ **Admin Controls**: Complete user and project management system

#### **🔧 Technical Features**

- ✅ **MongoDB Atlas**: Cloud database with production-ready scaling
- ✅ **JWT Authentication**: Secure token-based authentication system
- ✅ **File Upload System**: Secure project file handling and storage
- ✅ **Email Notifications**: Automated notifications for purchases and updates
- ✅ **SEO Optimized**: Comprehensive SEO implementation for better search visibility
- ✅ **Mobile Responsive**: Optimized for all device sizes
- ✅ **Dark Theme**: Modern dark UI with professional design
- ✅ **Performance Optimized**: Fast loading with code splitting and lazy loading

## 📸 **Screenshots & Demo**

### **🏠 Homepage**

- Modern landing page with featured projects
- 3D animated ProjectBuzz logo with Rubik's cube design
- Dark theme with professional corporate styling

### **🛒 Marketplace**

- Grid layout with project cards showing images, pricing, and details
- Advanced filtering and search functionality
- Category-based browsing (Web, Mobile, Desktop, AI/ML, Blockchain, Games)

### **👤 User Dashboards**

- **Buyer Dashboard**: Purchase history, downloaded projects, payment tracking
- **Seller Dashboard**: Project management, sales analytics, wallet balance
- **Admin Dashboard**: User management, project approval, system analytics

### **💳 Payment Flow**

- Integrated Razorpay checkout with multiple payment options
- Instant project access after successful payment
- Automated email confirmations and receipts

## 🌐 **Live Demo**

**Experience ProjectBuzz**: [https://projectbuzz.tech](https://projectbuzz.tech)

### **Test Accounts** (Demo purposes)

- **Buyer Account**: Register as buyer to browse and purchase projects
- **Seller Account**: Register as seller to list and sell your projects
- **Payment Testing**: Use Razorpay test cards for payment simulation

## 🧪 **Testing**

ProjectBuzz includes a comprehensive testing suite with unit, integration, and end-to-end tests.

### **Test Structure**

```
tests/
├── integration/          # API and workflow integration tests
├── unit/                 # Individual component/function tests
└── e2e/                  # End-to-end user journey tests

frontend/src/__tests__/   # Frontend component tests
backend/tests/            # Backend API and service tests
```

### **Running Tests**

```bash
# All tests
npm test

# Frontend tests only
cd frontend && npm test

# Backend tests only
cd backend && npm test

# Integration tests
cd tests && npm run test:integration

# With coverage
npm run test:coverage
```

## 🚀 **Setup Instructions**

### Prerequisites

- Node.js (v18 or higher)
- MongoDB Atlas account (for production) or local MongoDB (for development)
- Razorpay account for payment processing
- Gmail account with App Password for email notifications

### Quick Start (Development)

1. **Install all dependencies**

   ```bash
   npm run install:all
   ```

2. **Setup environment files**

   ```bash
   npm run setup
   ```

3. **Start development servers**
   ```bash
   npm run dev
   ```

### Backend Setup

1. **Navigate to backend directory**

   ```bash
   cd backend
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Configuration**

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration:

   ```env
   # MongoDB Configuration
   MONGO_URI=mongodb://localhost:27017/projectbuzz

   # Server Configuration
   PORT=5000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:5173

   # JWT Configuration
   JWT_SECRET=your-super-secret-jwt-key-here

   # Razorpay Configuration
   RAZORPAY_KEY_ID=your_razorpay_key_id
   RAZORPAY_KEY_SECRET=your_razorpay_key_secret

   # Email Configuration
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-gmail-app-password
   ```

4. **Start the backend server**
   ```bash
   npm run dev
   ```

### Frontend Setup

1. **Navigate to frontend directory**

   ```bash
   cd frontend
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create `.env` file:

   ```env
   VITE_API_URL=http://localhost:5000/api
   VITE_RAZORPAY_KEY_ID=your_razorpay_key_id
   VITE_APP_NAME=ProjectBuzz
   ```

4. **Start the frontend development server**
   ```bash
   npm run dev
   ```

## Production Deployment

### MongoDB Atlas Migration

For production deployment, migrate from local MongoDB to MongoDB Atlas:

1. **Run the migration script**

   ```bash
   npm run atlas:migrate
   ```

2. **Setup Atlas database**

   ```bash
   npm run atlas:setup
   ```

3. **Validate production configuration**

   ```bash
   npm run production:validate
   ```

4. **Verify deployment readiness**
   ```bash
   npm run production:verify
   ```

### Production Environment

1. **Create production environment file**

   ```bash
   cp backend/.env.production.example backend/.env.production
   ```

2. **Update with production values**

   ```env
   NODE_ENV=production
   MONGO_URI=mongodb+srv://username:<EMAIL>/projectbuzz
   FRONTEND_URL=https://your-domain.com
   BACKEND_URL=https://api.your-domain.com
   JWT_SECRET=your-production-jwt-secret-64-characters-minimum
   RAZORPAY_KEY_ID=rzp_live_your_production_key
   RAZORPAY_KEY_SECRET=your_production_secret
   ```

3. **Deploy with PM2**
   ```bash
   cd backend
   pm2 start ecosystem.config.js --env production
   ```

For detailed deployment instructions, see [PRODUCTION-DEPLOYMENT.md](./PRODUCTION-DEPLOYMENT.md)

## API Endpoints

### Authentication

All protected endpoints require JWT token in Authorization header:

```
Authorization: Bearer <jwt-token>
```

### Projects API

- `GET /api/projects` - Get all approved projects (public)
- `GET /api/projects/my` - Get current user's projects (seller)
- `GET /api/projects/:id` - Get single project
- `POST /api/projects` - Create new project (seller, with file upload)
- `PUT /api/projects/:id` - Update project (seller)
- `DELETE /api/projects/:id` - Delete project (seller)
- `POST /api/projects/:id/purchase` - Purchase project (buyer)
- `GET /api/projects/:id/download` - Download purchased project

### Users API

- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update user profile
- `GET /api/users/me/purchases` - Get user's purchases
- `GET /api/users/me/sales` - Get user's sales (seller)
- `GET /api/users/me/stats` - Get user statistics
- `GET /api/users/:id` - Get public user profile
- `GET /api/users` - Get all users (admin)

## File Upload Process

1. **Frontend**: User selects file in form
2. **Frontend**: File sent to backend via multipart/form-data
3. **Backend**: Multer processes file upload
4. **Backend**: File uploaded to Firebase Storage
5. **Backend**: File metadata saved to MongoDB
6. **Backend**: Public download URL returned

## Database Models

### User Model

```javascript
{
  firebaseUid: String,
  email: String,
  displayName: String,
  role: ['buyer', 'seller', 'admin'],
  stats: {
    projectsPurchased: Number,
    projectsSold: Number,
    totalSpent: Number,
    totalEarned: Number
  }
}
```

### Project Model

```javascript
{
  title: String,
  description: String,
  price: Number,
  file: {
    url: String,
    filename: String,
    originalName: String
  },
  seller: ObjectId,
  buyers: [{ user: ObjectId, purchasedAt: Date }],
  status: ['pending', 'approved', 'rejected'],
  category: String,
  tags: [String]
}
```

## Testing the Integration

1. **Start both servers** (backend on :5000, frontend on :5173)
2. **Register/Login** using Firebase Auth
3. **Switch to seller role** in user profile
4. **Create a project** with file upload
5. **Switch to buyer role**
6. **Purchase and download** the project

## New Components

- `SellerDashboardNew.tsx` - Backend-integrated seller dashboard
- `BuyerDashboardNew.tsx` - Backend-integrated buyer dashboard
- `projectService.js` - API service for projects
- `userService.js` - API service for users

## Migration Notes

The original components (`SellerDashboard.tsx`, `BuyerDashboard.tsx`) use direct Firebase calls. The new components (`*New.tsx`) use the backend API. You can gradually migrate by updating the route imports in `App.tsx`.

## Security Features

- Firebase token verification on all protected routes
- Role-based access control
- File type and size validation
- Input sanitization and validation
- CORS configuration
- Helmet security headers

## 🐛 **Troubleshooting**

1. **CORS errors**: Check `FRONTEND_URL` in backend `.env`
2. **Auth errors**: Verify JWT secret and MongoDB connection
3. **File upload errors**: Check file size limits and storage permissions
4. **MongoDB errors**: Ensure MongoDB Atlas connection string is correct
5. **Payment errors**: Verify Razorpay API keys and webhook configuration

## 📊 **Project Statistics**

- **Total Lines of Code**: 50,000+
- **Components**: 100+ React components
- **API Endpoints**: 30+ RESTful endpoints
- **Database Collections**: 5 main collections
- **Deployment**: Production-ready on Vercel + Render
- **Performance**: 90+ Lighthouse score

## 🤝 **Contributing**

We welcome contributions to ProjectBuzz! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit your changes**: `git commit -m 'Add amazing feature'`
4. **Push to the branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 **Developer**

**Aniruddha Gayki**

- **GitHub**: [@Aniruddha434](https://github.com/Aniruddha434)
- **Email**: <EMAIL>
- **LinkedIn**: [Connect with me](https://linkedin.com/in/aniruddha-gayki)

## 🌟 **Support the Project**

If you find ProjectBuzz helpful, please consider:

- ⭐ **Starring the repository**
- 🐛 **Reporting bugs and issues**
- 💡 **Suggesting new features**
- 🤝 **Contributing to the codebase**
- 📢 **Sharing with the developer community**

---

**Built with ❤️ by Aniruddha Gayki | © 2025 ProjectBuzz. All rights reserved.**

**🔗 Live Demo**: [https://projectbuzz.tech](https://projectbuzz.tech)
