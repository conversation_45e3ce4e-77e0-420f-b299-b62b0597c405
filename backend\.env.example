# MongoDB Configuration
# Local Development
MONGO_URI=mongodb://localhost:27017/projectbuzz

# Production MongoDB Atlas (replace with your Atlas connection string)
# MONGO_URI=mongodb+srv://<username>:<password>@<cluster-name>.mongodb.net/projectbuzz?retryWrites=true&w=majority

# MongoDB Atlas Configuration
MONGODB_ATLAS_URI=mongodb+srv://<username>:<password>@<cluster-name>.mongodb.net/projectbuzz?retryWrites=true&w=majority
MONGODB_ATLAS_DB_NAME=projectbuzz

# Database Connection Settings
DB_MAX_POOL_SIZE=10
DB_SERVER_SELECTION_TIMEOUT=5000
DB_SOCKET_TIMEOUT=45000
DB_CONNECT_TIMEOUT=10000
DB_MAX_IDLE_TIME=30000

# Server Configuration
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:5000

# Production URLs (update for your production deployment)
PRODUCTION_FRONTEND_URL=https://your-domain.com
PRODUCTION_BACKEND_URL=https://api.your-domain.com

# Auto Port Detection (fallback ports if main port is busy)
PORT_FALLBACK_START=5001
PORT_FALLBACK_END=5010

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-minimum-32-characters-long
JWT_EXPIRES_IN=7d

# Razorpay Payment Gateway Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:5173,https://your-domain.com

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=Doma@ji12

# Email Settings
FROM_NAME=ProjectBuzz
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
APP_NAME=ProjectBuzz

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
MAX_FILES_PER_PROJECT=5

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
RATE_LIMIT_SKIP_FAILED_REQUESTS=false

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# Security Configuration
HELMET_ENABLED=true
TRUST_PROXY=false
SESSION_SECRET=your-super-secret-session-key-minimum-32-characters
COOKIE_SECURE=false
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=lax

# Production Security (enable for production)
# TRUST_PROXY=true
# COOKIE_SECURE=true
# COOKIE_SAME_SITE=strict

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# OAuth Callback URLs
GOOGLE_CALLBACK_URL=http://localhost:5000/auth/google/callback
GITHUB_CALLBACK_URL=http://localhost:5000/auth/github/callback

# Production OAuth Callbacks (update for production)
PRODUCTION_GOOGLE_CALLBACK_URL=https://api.projectbuzz.com/auth/google/callback
PRODUCTION_GITHUB_CALLBACK_URL=https://api.projectbuzz.com/auth/github/callback

# Performance Configuration
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
COMPRESSION_THRESHOLD=1024

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# Monitoring & Analytics
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=false
SENTRY_DSN=your-sentry-dsn-here

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
ENABLE_CACHING=false

# Production Cache (enable for production)
# REDIS_URL=redis://your-redis-instance:6379
# ENABLE_CACHING=true

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-key
WEBHOOK_TIMEOUT=30000

# File Storage Configuration
STORAGE_TYPE=local
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=projectbuzz-uploads

# Production File Storage (uncomment for production)
# STORAGE_TYPE=s3
# AWS_ACCESS_KEY_ID=your-production-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-production-aws-secret-key
