{"name": "backend", "version": "1.0.0", "description": "Backend server for the application", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "start:prod": "NODE_ENV=production node server.js", "start:staging": "NODE_ENV=staging node server.js", "dev": "NODE_ENV=development nodemon server.js", "dev:debug": "NODE_ENV=development DEBUG=* nodemon server.js", "build": "npm install", "deploy:prep": "node ../scripts/deploy-production.js", "test": "NODE_ENV=test jest", "test:unit": "NODE_ENV=test jest tests/unit", "test:integration": "NODE_ENV=test jest tests/integration", "test:watch": "NODE_ENV=test jest --watch", "test:coverage": "NODE_ENV=test jest --coverage", "health-check": "node health-check.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "logs": "tail -f logs/app.log", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:logs": "pm2 logs"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-validator": "^7.0.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.3.2", "morgan": "^1.10.0", "motion": "^12.16.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "razorpay": "^2.9.6", "redis": "^4.6.12", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"nodemon": "^3.1.0"}, "engines": {"node": "18.x"}}