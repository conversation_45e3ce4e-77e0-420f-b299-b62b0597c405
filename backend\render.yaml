services:
  - type: web
    name: projectbuzz-backend
    env: node
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
    # Add your environment variables here:
    # - key: MONGODB_URI
    #   value: your_mongodb_connection_string
    # - key: JWT_SECRET
    #   value: your_jwt_secret
    # - key: RAZORPAY_KEY_ID
    #   value: your_razorpay_key_id
    # - key: RAZORPAY_KEY_SECRET
    #   value: your_razorpay_secret
    # - key: GOOGLE_CLIENT_ID
    #   value: your_google_client_id
    # - key: GOOGLE_CLIENT_SECRET
    #   value: your_google_client_secret
    # - key: GITHUB_CLIENT_ID
    #   value: your_github_client_id
    # - key: GITHUB_CLIENT_SECRET
    #   value: your_github_client_secret
    # - key: EMAIL_USER
    #   value: <EMAIL>
    # - key: EMAIL_PASS
    #   value: your_gmail_app_password
    # - key: FRONTEND_URL
    #   value: https://your-vercel-app.vercel.app
    # - key: BACKEND_URL
    #   value: https://your-render-app.onrender.com
