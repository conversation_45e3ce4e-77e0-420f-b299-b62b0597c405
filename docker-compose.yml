version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: projectbuzz-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: ${MONGO_DB_NAME:-projectbuzz}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - projectbuzz-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: projectbuzz-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - projectbuzz-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: projectbuzz-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      MONGO_URI: mongodb://mongodb:27017/projectbuzz
      REDIS_URL: redis://redis:6379
      PORT: 5000
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
      JWT_SECRET: ${JWT_SECRET}
      RAZORPAY_KEY_ID: ${RAZORPAY_KEY_ID}
      RAZORPAY_KEY_SECRET: ${RAZORPAY_KEY_SECRET}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
    ports:
      - "5000:5000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - projectbuzz-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: projectbuzz-frontend
    restart: unless-stopped
    environment:
      VITE_API_URL: ${VITE_API_URL:-http://localhost:5000/api}
      VITE_BACKEND_URL: ${VITE_BACKEND_URL:-http://localhost:5000}
      VITE_RAZORPAY_KEY_ID: ${VITE_RAZORPAY_KEY_ID}
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - projectbuzz-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: MongoDB Express (Web-based MongoDB admin interface)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: projectbuzz-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_URL: mongodb://mongodb:27017/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - projectbuzz-network
    profiles:
      - dev

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  projectbuzz-network:
    driver: bridge
