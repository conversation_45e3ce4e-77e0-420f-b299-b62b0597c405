# 🎨 ProjectBuzz Branding Guidelines

## 🎯 Brand Identity

### **Logo Design**
- **Primary Logo**: 3D Rubik's cube with white/gradient colors
- **Background**: Black (#000000) for contrast
- **Typography**: Modern, clean sans-serif font
- **Tagline**: "Digital Marketplace"

### **Color Palette**
```css
/* Primary Colors */
--primary-black: #000000
--primary-white: #FFFFFF
--accent-blue: #3B82F6
--accent-gray: #6B7280

/* UI Colors */
--background: #111111
--card-background: #1F1F1F
--border: #374151
--text-primary: #FFFFFF
--text-secondary: #9CA3AF
```

### **Typography**
- **Primary Font**: Inter, system-ui, sans-serif
- **Headings**: Bold (600-700 weight)
- **Body Text**: Regular (400 weight)
- **Code**: Fira Code, monospace

## 📐 Logo Usage

### **Recommended Sizes**
- **GitHub Social Preview**: 1280x640px
- **Favicon**: 32x32px, 16x16px
- **Open Graph**: 1200x630px
- **Twitter Card**: 1200x600px

### **Logo Variations**
1. **Full Logo**: Icon + Text + Tagline
2. **Compact Logo**: Icon + Text only
3. **Icon Only**: Just the 3D cube
4. **Text Only**: ProjectBuzz wordmark

## 🖼️ Asset Creation Tools

### **Recommended Tools**
- **Canva**: For quick social media graphics
- **Figma**: For professional design work
- **GIMP/Photoshop**: For detailed image editing
- **Favicon.io**: For favicon generation

### **Banner Dimensions**
```
GitHub Repository Banner: 1280x640px
LinkedIn Post: 1200x627px
Twitter Header: 1500x500px
Facebook Cover: 1200x630px
```

## 📱 Social Media Assets

### **GitHub Repository**
- Add a custom social preview image
- Use consistent branding across all documentation
- Include logo in README header

### **Professional Presentation**
- Consistent color scheme
- Professional typography
- Clean, modern design aesthetic
- Dark theme preference

---

**Note**: Create actual logo files and replace placeholder images for maximum brand impact!
