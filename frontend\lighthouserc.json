{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/market", "http://localhost:3000/login", "http://localhost:3000/register"], "startServerCommand": "npm run preview", "startServerReadyPattern": "Local:.*:3000", "startServerReadyTimeout": 30000}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.8}], "categories:pwa": ["warn", {"minScore": 0.7}]}}, "upload": {"target": "temporary-public-storage"}}}