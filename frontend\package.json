{"name": "projectbuzz", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc --noEmit && vite build", "build:prod": "vite build --mode production", "build:staging": "vite build --mode staging", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:prod": "vite preview --mode production", "type-check": "tsc --noEmit", "analyze": "vite-bundle-analyzer dist", "clean": "rm -rf dist", "test": "vitest", "test:ui": "vitest --ui", "test:unit": "vitest run src/__tests__", "test:watch": "vitest watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@headlessui/react": "^2.2.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^9.88.13", "@react-three/fiber": "^8.15.12", "@types/three": "^0.178.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.15.0", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.28.0", "tailwind-merge": "^3.3.0", "three": "^0.160.0", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.17", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^5.4.10"}}