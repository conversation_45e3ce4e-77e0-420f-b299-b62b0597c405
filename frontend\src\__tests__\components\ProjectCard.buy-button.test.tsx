/**
 * Test: ProjectCard Buy Button Display
 * Tests that the buy button displays correctly in different scenarios
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import ProjectCard from '../../components/ProjectCard';

// Mock the AuthContext
const mockAuthContext = {
  user: null,
  isAuthenticated: false
};

vi.mock('../../context/AuthContext', () => ({
  useAuth: () => mockAuthContext
}));

// Mock the NegotiationButton component
vi.mock('../../components/NegotiationButton', () => ({
  NegotiationButton: ({ className, children, ...props }: any) => (
    <button className={className} data-testid="negotiation-button" {...props}>
      Negotiate
    </button>
  )
}));

// Test wrapper with Router
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('ProjectCard Buy Button Display', () => {
  const mockProject = {
    _id: '1',
    title: 'Test Project',
    description: 'This is a test project description',
    price: 99.99,
    images: [{ url: 'test-image.jpg', filename: 'test.jpg', originalName: 'test.jpg' }],
    seller: {
      _id: 'seller1',
      displayName: 'Test Seller'
    },
    category: 'web',
    tags: ['react', 'javascript'],
    createdAt: '2025-01-01T00:00:00.000Z',
    status: 'approved'
  };

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  test('shows "Sign in to Buy" button when user is not authenticated', () => {
    mockAuthContext.user = null;
    mockAuthContext.isAuthenticated = false;

    render(
      <TestWrapper>
        <ProjectCard 
          project={mockProject} 
          showBuyButton={true}
          isPurchased={false}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Sign in to Buy')).toBeInTheDocument();
  });

  test('shows buy button and negotiate button for authenticated buyer', () => {
    mockAuthContext.user = { _id: 'buyer1', role: 'buyer', displayName: 'Test Buyer' };
    mockAuthContext.isAuthenticated = true;

    render(
      <TestWrapper>
        <ProjectCard 
          project={mockProject} 
          showBuyButton={true}
          isPurchased={false}
          user={mockAuthContext.user}
        />
      </TestWrapper>
    );

    expect(screen.getByText(/Buy ₹99.99/)).toBeInTheDocument();
    expect(screen.getByTestId('negotiation-button')).toBeInTheDocument();
  });

  test('shows "Owned" status for purchased projects', () => {
    mockAuthContext.user = { _id: 'buyer1', role: 'buyer', displayName: 'Test Buyer' };
    mockAuthContext.isAuthenticated = true;

    render(
      <TestWrapper>
        <ProjectCard 
          project={mockProject} 
          showBuyButton={true}
          isPurchased={true}
          user={mockAuthContext.user}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Owned')).toBeInTheDocument();
  });

  test('buy button click triggers onClick callback', () => {
    const mockOnClick = vi.fn();
    mockAuthContext.user = { _id: 'buyer1', role: 'buyer', displayName: 'Test Buyer' };

    render(
      <TestWrapper>
        <ProjectCard 
          project={mockProject} 
          showBuyButton={true}
          isPurchased={false}
          user={mockAuthContext.user}
          onClick={mockOnClick}
        />
      </TestWrapper>
    );

    const buyButton = screen.getByText(/Buy ₹99.99/);
    fireEvent.click(buyButton);

    expect(mockOnClick).toHaveBeenCalledWith(mockProject);
  });

  test('does not show buttons when showBuyButton is false', () => {
    render(
      <TestWrapper>
        <ProjectCard 
          project={mockProject} 
          showBuyButton={false}
          isPurchased={false}
        />
      </TestWrapper>
    );

    expect(screen.queryByText(/Buy/)).not.toBeInTheDocument();
    expect(screen.queryByTestId('negotiation-button')).not.toBeInTheDocument();
  });
});
