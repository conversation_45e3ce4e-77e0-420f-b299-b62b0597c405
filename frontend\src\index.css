@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fix for SVG attribute validation errors with Framer Motion */
svg[width="auto"] {
  width: unset !important;
}

svg[height="auto"] {
  height: unset !important;
}

/* Ensure SVG elements don't receive invalid auto values */
svg {
  width: auto;
  height: auto;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.9%;
  }
}

/* Modern Dashboard Styles */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.bg-gray-750 {
  background-color: #374151;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Professional Black Modal Theme */
.project-modal-black {
  background-color: #000000 !important;
  color: #ffffff !important;
}

.project-modal-black * {
  border-color: #374151 !important;
}

.project-modal-black h1,
.project-modal-black h2,
.project-modal-black h3,
.project-modal-black h4 {
  color: #ffffff !important;
}

.project-modal-black p,
.project-modal-black span {
  color: #d1d5db !important;
}

.project-modal-black .bg-white,
.project-modal-black .bg-gray-50,
.project-modal-black .bg-gray-100 {
  background-color: #111827 !important;
}

.project-modal-black .bg-gray-800,
.project-modal-black .bg-gray-900 {
  background-color: #000000 !important;
}

.project-modal-black .text-gray-900 {
  color: #ffffff !important;
}

.project-modal-black .text-gray-600,
.project-modal-black .text-gray-500 {
  color: #9ca3af !important;
}

.project-modal-black .border-gray-200,
.project-modal-black .border-gray-300 {
  border-color: #374151 !important;
}

/* Badge and Button Overrides */
.project-modal-black .bg-orange-100 {
  background-color: rgba(194, 65, 12, 0.3) !important;
  color: #fed7aa !important;
}

.project-modal-black .bg-green-100 {
  background-color: rgba(21, 128, 61, 0.3) !important;
  color: #bbf7d0 !important;
}

.project-modal-black .bg-blue-100,
.project-modal-black .bg-blue-50 {
  background-color: rgba(29, 78, 216, 0.3) !important;
  color: #bfdbfe !important;
}

.project-modal-black .bg-yellow-100 {
  background-color: rgba(161, 98, 7, 0.3) !important;
  color: #fef3c7 !important;
}

.project-modal-black .bg-red-100 {
  background-color: rgba(185, 28, 28, 0.3) !important;
  color: #fecaca !important;
}

.project-modal-black .bg-purple-100 {
  background-color: rgba(126, 34, 206, 0.3) !important;
  color: #e9d5ff !important;
}

.project-modal-black .bg-indigo-50 {
  background-color: rgba(67, 56, 202, 0.3) !important;
  color: #c7d2fe !important;
}

/* Text Color Overrides */
.project-modal-black .text-orange-800 {
  color: #fed7aa !important;
}

.project-modal-black .text-green-800 {
  color: #bbf7d0 !important;
}

.project-modal-black .text-blue-800,
.project-modal-black .text-blue-700 {
  color: #bfdbfe !important;
}

.project-modal-black .text-yellow-800 {
  color: #fef3c7 !important;
}

.project-modal-black .text-red-800 {
  color: #fecaca !important;
}

.project-modal-black .text-purple-800 {
  color: #e9d5ff !important;
}

.project-modal-black .text-indigo-800 {
  color: #c7d2fe !important;
}

/* Force all modal content to be dark */
[data-modal="project-details"] {
  background-color: #000000 !important;
  color: #ffffff !important;
}

[data-modal="project-details"] * {
  color: inherit !important;
}

[data-modal="project-details"] h1,
[data-modal="project-details"] h2,
[data-modal="project-details"] h3,
[data-modal="project-details"] h4 {
  color: #ffffff !important;
}

[data-modal="project-details"] .bg-white {
  background-color: #111827 !important;
}

/* Override any remaining light backgrounds */
.fixed.inset-0.z-50 div[class*="bg-white"],
.fixed.inset-0.z-50 div[class*="bg-gray-50"],
.fixed.inset-0.z-50 div[class*="bg-gray-100"] {
  background-color: #111827 !important;
  color: #ffffff !important;
}

/* Comprehensive text visibility fixes for project modal */
[data-modal="project-details"] h1,
[data-modal="project-details"] h2,
[data-modal="project-details"] h3,
[data-modal="project-details"] h4,
[data-modal="project-details"] h5,
[data-modal="project-details"] h6 {
  color: #ffffff !important;
}

[data-modal="project-details"] p,
[data-modal="project-details"] span:not([style*="color"]),
[data-modal="project-details"] div:not([style*="color"]) {
  color: #d1d5db !important;
}

[data-modal="project-details"] .text-gray-400,
[data-modal="project-details"] .text-gray-500,
[data-modal="project-details"] .text-gray-600 {
  color: #9ca3af !important;
}

[data-modal="project-details"] .text-gray-300 {
  color: #d1d5db !important;
}

[data-modal="project-details"] .text-white {
  color: #ffffff !important;
}

/* Force visibility for any remaining invisible text */
[data-modal="project-details"] * {
  color: inherit !important;
}

/* Ensure buttons have proper text color */
[data-modal="project-details"] button {
  color: #ffffff !important;
}

/* Fix any remaining text color issues */
.fixed.inset-0.z-50 * {
  color: inherit;
}

.fixed.inset-0.z-50 h1,
.fixed.inset-0.z-50 h2,
.fixed.inset-0.z-50 h3,
.fixed.inset-0.z-50 h4 {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 p,
.fixed.inset-0.z-50 span {
  color: #d1d5db !important;
}

/* Ultra-aggressive text visibility fixes */
.fixed.inset-0.z-50 .overflow-y-auto * {
  color: inherit !important;
}

.fixed.inset-0.z-50 .overflow-y-auto h1,
.fixed.inset-0.z-50 .overflow-y-auto h2,
.fixed.inset-0.z-50 .overflow-y-auto h3,
.fixed.inset-0.z-50 .overflow-y-auto h4 {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 .overflow-y-auto p,
.fixed.inset-0.z-50 .overflow-y-auto span:not([style*="color"]),
.fixed.inset-0.z-50 .overflow-y-auto div:not([style*="color"]) {
  color: #d1d5db !important;
}

/* Force all text in modal to be visible */
.fixed.inset-0.z-50 .max-w-7xl * {
  color: inherit !important;
}

.fixed.inset-0.z-50 .max-w-7xl h1,
.fixed.inset-0.z-50 .max-w-7xl h2,
.fixed.inset-0.z-50 .max-w-7xl h3,
.fixed.inset-0.z-50 .max-w-7xl h4 {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 .max-w-7xl p,
.fixed.inset-0.z-50 .max-w-7xl span,
.fixed.inset-0.z-50 .max-w-7xl div {
  color: #d1d5db !important;
}

/* Override any remaining invisible text */
.fixed.inset-0.z-50 .max-w-7xl .text-black,
.fixed.inset-0.z-50 .max-w-7xl .text-gray-900,
.fixed.inset-0.z-50 .max-w-7xl .text-gray-800 {
  color: #ffffff !important;
}

/* Final catch-all for any remaining invisible text */
.fixed.inset-0.z-50 .rounded-2xl * {
  color: inherit !important;
}

.fixed.inset-0.z-50 .rounded-2xl h1,
.fixed.inset-0.z-50 .rounded-2xl h2,
.fixed.inset-0.z-50 .rounded-2xl h3,
.fixed.inset-0.z-50 .rounded-2xl h4,
.fixed.inset-0.z-50 .rounded-2xl h5,
.fixed.inset-0.z-50 .rounded-2xl h6 {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 .rounded-2xl p,
.fixed.inset-0.z-50 .rounded-2xl span,
.fixed.inset-0.z-50 .rounded-2xl div,
.fixed.inset-0.z-50 .rounded-2xl label,
.fixed.inset-0.z-50 .rounded-2xl a {
  color: #d1d5db !important;
}

/* Ensure button text is always visible */
.fixed.inset-0.z-50 .rounded-2xl button {
  color: #ffffff !important;
}

/* Force visibility for any text that might still be hidden */
.fixed.inset-0.z-50 .rounded-2xl [class*="text-"] {
  color: #d1d5db !important;
}

.fixed.inset-0.z-50 .rounded-2xl [class*="text-"] h1,
.fixed.inset-0.z-50 .rounded-2xl [class*="text-"] h2,
.fixed.inset-0.z-50 .rounded-2xl [class*="text-"] h3,
.fixed.inset-0.z-50 .rounded-2xl [class*="text-"] h4 {
  color: #ffffff !important;
}

/* Universal text visibility fixes for all projects */
.fixed.inset-0.z-50 [style*="color: #22c55e"] {
  color: #22c55e !important;
}

.fixed.inset-0.z-50 [style*="color: #bbf7d0"] {
  color: #bbf7d0 !important;
}

.fixed.inset-0.z-50 [style*="color: #dcfce7"] {
  color: #dcfce7 !important;
}

.fixed.inset-0.z-50 [style*="color: #ffffff"] {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 [style*="color: #d1d5db"] {
  color: #d1d5db !important;
}

.fixed.inset-0.z-50 [style*="color: #e5e7eb"] {
  color: #e5e7eb !important;
}

.fixed.inset-0.z-50 [style*="color: #9ca3af"] {
  color: #9ca3af !important;
}

/* Force visibility for specific green text elements */
.fixed.inset-0.z-50 .text-green-300,
.fixed.inset-0.z-50 .text-green-400,
.fixed.inset-0.z-50 .text-green-500 {
  color: #22c55e !important;
}

/* Systematic text visibility for all projects */
.fixed.inset-0.z-50 .text-white {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 .font-medium {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 .font-bold {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 .font-semibold {
  color: #ffffff !important;
}

/* Remove text shadows and ensure visibility */
.fixed.inset-0.z-50 * {
  text-shadow: none !important;
}

.fixed.inset-0.z-50 .whitespace-pre-wrap {
  color: #e5e7eb !important;
}

/* Fix for installation and usage instruction text */
.fixed.inset-0.z-50 div[style*="color: #e5e7eb"] {
  color: #e5e7eb !important;
}

.fixed.inset-0.z-50 div[style*="color: #f3e8ff"] {
  color: #f3e8ff !important;
}

/* Content section fixes for all projects */
.fixed.inset-0.z-50 .p-4.rounded-lg h4 {
  color: #ffffff !important;
}

.fixed.inset-0.z-50 .p-4.rounded-lg .text-sm {
  color: #e5e7eb !important;
}

.fixed.inset-0.z-50 .p-4.rounded-lg .whitespace-pre-wrap {
  color: #e5e7eb !important;
}

/* Specific fix for installation instructions */
.fixed.inset-0.z-50 div[style*="backgroundColor: '#111827'"] .text-sm {
  color: #e5e7eb !important;
}

.fixed.inset-0.z-50 div[style*="backgroundColor: '#111827'"] h4 {
  color: #d1d5db !important;
}

/* Specific fix for usage instructions */
.fixed.inset-0.z-50
  div[style*="backgroundColor: 'rgba(126, 34, 206, 0.3)'"]
  .text-sm {
  color: #f3e8ff !important;
}

.fixed.inset-0.z-50
  div[style*="backgroundColor: 'rgba(126, 34, 206, 0.3)'"]
  h4 {
  color: #e9d5ff !important;
}

/* Universal project modal text visibility system */
/* This system works for ALL projects regardless of content type */

/* Primary text colors - headings and titles */
.fixed.inset-0.z-50 h1,
.fixed.inset-0.z-50 h2,
.fixed.inset-0.z-50 h3,
.fixed.inset-0.z-50 h4,
.fixed.inset-0.z-50 h5,
.fixed.inset-0.z-50 h6 {
  color: #ffffff !important;
}

/* Body text and descriptions */
.fixed.inset-0.z-50 p,
.fixed.inset-0.z-50 .text-sm,
.fixed.inset-0.z-50 .text-lg {
  color: #d1d5db !important;
}

/* Green text elements - success states and downloads */
.fixed.inset-0.z-50 .text-green-300,
.fixed.inset-0.z-50 .text-green-400,
.fixed.inset-0.z-50 .text-green-500,
.fixed.inset-0.z-50 span[style*="color: #22c55e"],
.fixed.inset-0.z-50 span[style*="color: #bbf7d0"],
.fixed.inset-0.z-50 p[style*="color: #22c55e"] {
  color: #22c55e !important;
}

/* White text elements - important content */
.fixed.inset-0.z-50 .text-white,
.fixed.inset-0.z-50 .font-medium,
.fixed.inset-0.z-50 .font-bold,
.fixed.inset-0.z-50 .font-semibold,
.fixed.inset-0.z-50 span[style*="color: #ffffff"],
.fixed.inset-0.z-50 p[style*="color: #ffffff"] {
  color: #ffffff !important;
}

/* Content in instruction boxes */
.fixed.inset-0.z-50 .whitespace-pre-wrap,
.fixed.inset-0.z-50 div[style*="color: #e5e7eb"],
.fixed.inset-0.z-50 div[style*="color: #f3e8ff"] {
  color: #e5e7eb !important;
}

/* Secondary text - stats, dates, file info */
.fixed.inset-0.z-50 .text-gray-400,
.fixed.inset-0.z-50 .text-gray-500,
.fixed.inset-0.z-50 span[style*="color: #9ca3af"] {
  color: #9ca3af !important;
}

/* Ensure all text is visible and readable */
.fixed.inset-0.z-50 * {
  text-shadow: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Button text visibility */
.fixed.inset-0.z-50 button {
  color: #ffffff !important;
}

/* Override any remaining invisible text */
.fixed.inset-0.z-50 .overflow-y-auto * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Additional coverage for all project types */
/* Badge text visibility */
.fixed.inset-0.z-50 .px-2.py-1,
.fixed.inset-0.z-50 .px-3.py-1 {
  color: inherit !important;
}

/* Technology tags and badges */
.fixed.inset-0.z-50 .bg-blue-100,
.fixed.inset-0.z-50 .bg-green-100,
.fixed.inset-0.z-50 .bg-orange-100,
.fixed.inset-0.z-50 .bg-purple-100,
.fixed.inset-0.z-50 .bg-yellow-100 {
  color: inherit !important;
}

/* File and documentation text */
.fixed.inset-0.z-50 .text-xs {
  color: #9ca3af !important;
}

.fixed.inset-0.z-50 .capitalize {
  color: inherit !important;
}

/* Project description and content */
.fixed.inset-0.z-50 .leading-relaxed {
  color: #d1d5db !important;
}

/* Stats and numbers */
.fixed.inset-0.z-50 .text-2xl {
  color: inherit !important;
}

/* ===== COMPREHENSIVE MODAL SYSTEM - NO CONFLICTS ===== */

/* Z-Index Hierarchy:
   - Critical Modals (Negotiation): 99999-100000
   - Payment Modals: 50000-50001
   - Standard Modals (Share, etc): 40000-40001
   - Image Modals: 30000-30001
   - Project Detail Modals: 20000-20001
   - Project Cards: 1
*/

/* Critical Priority Modals (Negotiation) */
.modal-critical-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 99999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 1rem !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

.modal-critical-content {
  position: relative !important;
  z-index: 100000 !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  width: 100% !important;
  max-width: 28rem !important;
  margin: 0 auto !important;
  transform: none !important;
  box-sizing: border-box !important;
}

/* Payment Modals */
.modal-payment-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 50000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-payment-content {
  position: relative;
  z-index: 50001 !important;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 48rem;
}

/* Standard Modals (Share, etc) */
.modal-standard-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 40000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-standard-content {
  position: relative;
  z-index: 40001 !important;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 28rem;
}

/* Image Modals */
.modal-image-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 30000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-image-content {
  position: relative;
  z-index: 30001 !important;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 80rem;
}

/* Project Detail Modals */
.modal-detail-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 20000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-detail-content {
  position: relative;
  z-index: 20001 !important;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 80rem;
}

/* Legacy support - redirect to payment modals */
.modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 50000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  position: relative;
  z-index: 50001 !important;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 48rem;
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden !important;
}

/* Project cards - low z-index to stay below modals */
.project-card {
  position: relative;
  z-index: 1;
  /* Ensure project cards don't create stacking contexts that interfere with modals */
  isolation: auto;
  contain: none;
}

/* Project grid containers - ensure they don't interfere with modal positioning */
.homepage-projects-grid,
.project-card-container {
  /* Prevent grid containers from creating stacking contexts */
  position: relative;
  z-index: auto;
  isolation: auto;
  contain: none;
  /* Ensure overflow doesn't clip modals */
  overflow: visible;
}

/* Ensure no stacking context conflicts */
.project-card-container {
  position: relative;
  z-index: 1;
  isolation: auto;
}

/* Ensure modals are completely isolated from parent containers */
.modal-critical-backdrop,
.modal-payment-backdrop,
.modal-standard-backdrop,
.modal-image-backdrop,
.modal-detail-backdrop {
  /* Force modal to be positioned relative to viewport, not parent */
  position: fixed !important;
  /* Ensure modal covers entire viewport */
  inset: 0 !important;
  /* Remove any inherited transforms or positioning */
  transform: none !important;
  /* Ensure proper stacking */
  isolation: isolate !important;
  /* Prevent any parent overflow from affecting modal */
  contain: layout style paint !important;
}

/* Ensure modal content is properly centered regardless of parent */
.modal-critical-content,
.modal-payment-content,
.modal-standard-content,
.modal-image-content,
.modal-detail-content {
  /* Remove any inherited positioning */
  position: relative !important;
  /* Ensure proper centering */
  margin: 0 auto !important;
  /* Remove any transforms that might offset positioning */
  transform: none !important;
  /* Ensure content doesn't inherit parent constraints */
  max-width: none !important;
  /* Reset any inherited dimensions */
  left: auto !important;
  right: auto !important;
  top: auto !important;
  bottom: auto !important;
}

/* Specific sizing for each modal type */
.modal-critical-content {
  max-width: 28rem !important;
}

.modal-payment-content {
  max-width: 48rem !important;
}

.modal-standard-content {
  max-width: 28rem !important;
}

.modal-image-content {
  max-width: 80rem !important;
}

.modal-detail-content {
  max-width: 80rem !important;
}

/* Ensure grid layouts don't interfere with modal positioning */
.grid {
  /* Prevent grid containers from creating stacking contexts */
  isolation: auto;
  contain: none;
}

/* Specific fix for homepage and dashboard grids */
.homepage-projects-grid.grid,
.grid.grid-cols-1,
.grid.grid-cols-2,
.grid.grid-cols-3,
.grid.grid-cols-4 {
  /* Ensure grid containers don't clip or constrain modals */
  overflow: visible !important;
  position: relative;
  z-index: auto;
  isolation: auto;
  contain: none;
}

/* Ensure modal portals are never clipped by any parent container */
body > div[class*="modal-critical-backdrop"],
body > div[class*="modal-payment-backdrop"],
body > div[class*="modal-standard-backdrop"],
body > div[class*="modal-image-backdrop"],
body > div[class*="modal-detail-backdrop"] {
  /* Force modals to be completely independent of any parent styling */
  position: fixed !important;
  inset: 0 !important;
  z-index: 99999 !important;
  isolation: isolate !important;
  contain: layout style paint !important;
}

/* Essential project detail modal text visibility */
.fixed.inset-0.z-50 {
  color: #ffffff;
}

.fixed.inset-0.z-50 * {
  color: inherit;
  opacity: 1;
  visibility: visible;
}

/* ===== NAVBAR SPACING FIX - CHROME COMPATIBLE ===== */

/* Enhanced navbar height compensation with browser-specific fixes */
.page-with-navbar {
  padding-top: 4rem; /* 64px - matches navbar h-16 */
  /* Chrome-specific fix for viewport calculation differences */
  min-height: calc(100vh - 4rem);
}

/* For pages that need extra spacing */
.page-with-navbar-extra {
  padding-top: 5rem; /* 80px - for pages with dense content */
  min-height: calc(100vh - 5rem);
}

/* For dashboard pages with their own headers */
.dashboard-page {
  padding-top: 5rem; /* 80px - matches navbar h-16 + extra space for dashboard headers */
  min-height: calc(100vh - 5rem);
}

/* Ensure main content areas don't overlap with fixed navbar */
.main-content {
  margin-top: 4rem; /* 64px - matches navbar h-16 */
  position: relative;
  z-index: 1;
}

/* Specific fix for dashboard navbar overlap */
.dashboard-navbar-fix {
  padding-top: 6rem !important; /* 96px - ensures no overlap with fixed navbar */
  min-height: calc(100vh - 6rem) !important;
}

/* Specific fix for login/auth pages */
.login-page-navbar-fix {
  padding-top: 4rem; /* 64px - matches navbar h-16 */
  min-height: calc(100vh - 4rem);
  /* Ensure proper positioning and prevent overlap */
  position: relative;
  z-index: 1;
}

/* Chrome-specific navbar fixes */
@supports (-webkit-appearance: none) {
  /* Chrome/Safari specific styles */
  .page-with-navbar {
    padding-top: 4.25rem; /* Slightly more padding for Chrome */
  }

  .page-with-navbar-extra {
    padding-top: 5.25rem;
  }

  .dashboard-page {
    padding-top: 5.25rem;
  }

  .main-content {
    margin-top: 4.25rem;
  }

  .dashboard-navbar-fix {
    padding-top: 6.25rem !important;
  }

  /* Chrome-specific login page fix */
  .login-page-navbar-fix {
    padding-top: 4.25rem; /* Slightly more padding for Chrome */
    min-height: calc(100vh - 4.25rem);
  }
}

/* ===== MOBILE-RESPONSIVE DESIGN SYSTEM ===== */

/* Mobile-first responsive breakpoints */
:root {
  --mobile-min-touch-target: 44px;
  --mobile-padding: 1rem;
  --tablet-padding: 1.5rem;
  --desktop-padding: 2rem;
  --card-gap-mobile: 1rem;
  --card-gap-tablet: 1.25rem;
  --card-gap-desktop: 1.75rem;
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
  .page-with-navbar,
  .page-with-navbar-extra,
  .dashboard-page,
  .main-content {
    padding-top: 4rem; /* Keep consistent on mobile */
    padding-left: var(--mobile-padding);
    padding-right: var(--mobile-padding);
  }

  .dashboard-navbar-fix {
    padding-top: 5rem !important; /* Slightly less on mobile */
  }

  /* Chrome mobile fix */
  @supports (-webkit-appearance: none) {
    .page-with-navbar,
    .page-with-navbar-extra,
    .dashboard-page,
    .main-content {
      padding-top: 4.25rem;
    }

    .dashboard-navbar-fix {
      padding-top: 5.25rem !important;
    }
  }
}

/* ===== UNIFIED PROJECT CARD SYSTEM ===== */

/* Base project card styles - Image Dominant Design */
.project-card-unified {
  @apply bg-gray-900 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 border border-gray-800 hover:border-gray-700 flex flex-col;
  position: relative;
  z-index: 1;
  width: 100%;
  height: 320px; /* Increased height to accommodate buttons */
  overflow: visible; /* Changed from hidden to visible to prevent button clipping */
}

.project-card-unified::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.5rem;
  overflow: hidden;
  z-index: -1;
}

.project-card-unified:hover {
  transform: translateY(-2px);
}

/* Mobile-optimized project card */
@media (max-width: 640px) {
  .project-card-unified {
    height: 300px; /* Increased for mobile to accommodate stacked buttons */
    border-radius: 0.5rem;
  }

  .project-card-unified:hover {
    transform: none; /* Disable hover transform on mobile */
  }
}

/* Project card image container - 75% to give more space for buttons */
.project-card-image {
  @apply relative bg-muted overflow-hidden cursor-pointer;
  height: 75%; /* Reduced to 75% to give more space for buttons */
  flex-shrink: 0;
}

@media (max-width: 640px) {
  .project-card-image {
    height: 70%; /* Even less on mobile for stacked buttons */
  }
}

/* Project card content - 25% with action buttons */
.project-card-content {
  @apply p-3 flex flex-col justify-between;
  height: 25%; /* Increased to 25% for better button space */
  flex-shrink: 0;
  min-height: 80px; /* Increased to ensure space for buttons */
  position: relative;
  z-index: 5; /* Higher z-index for content area */
  background: transparent;
  overflow: visible; /* Ensure buttons aren't clipped */
}

@media (max-width: 640px) {
  .project-card-content {
    @apply p-2;
    height: 30%; /* More space on mobile for stacked buttons */
    min-height: 90px; /* Increased for mobile */
  }
}

/* Remove header - keep content minimal */
.project-card-header {
  display: none; /* Hide header for cleaner look */
}

/* Project card title - compact */
.project-card-title {
  @apply text-sm font-semibold text-white mb-1 line-clamp-1;
  font-size: 0.875rem; /* Smaller, more compact */
}

@media (max-width: 640px) {
  .project-card-title {
    @apply text-xs;
    font-size: 0.75rem;
  }
}

/* Project card description - minimal */
.project-card-description {
  @apply text-xs text-gray-400 line-clamp-1;
  margin: 0; /* Remove margins for compact design */
}

@media (max-width: 640px) {
  .project-card-description {
    font-size: 0.6875rem; /* Even smaller on mobile */
  }
}

/* Project card price - compact overlay */
.project-card-price {
  @apply text-sm font-bold text-green-400;
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  backdrop-filter: blur(4px);
}

@media (max-width: 640px) {
  .project-card-price {
    @apply text-xs;
    padding: 3px 6px;
  }
}

/* Sales data display */
.project-card-stats {
  @apply flex items-center space-x-2 text-xs text-gray-400;
}

@media (max-width: 640px) {
  .project-card-stats {
    @apply text-xs space-x-1;
  }

  .project-card-stats .stat-item {
    @apply flex items-center;
  }

  .project-card-stats .stat-icon {
    @apply h-3 w-3 mr-1;
  }
}

/* Project card action buttons */
.project-card-actions {
  @apply mt-auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
  z-index: 10; /* Higher z-index to ensure buttons are above other elements */
}

@media (max-width: 640px) {
  .project-card-actions {
    gap: 0.75rem;
  }
}

/* Project card buttons - unified styling */
.project-card-button {
  @apply py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center;
  min-height: var(--mobile-min-touch-target);
  position: relative;
  z-index: 11; /* Even higher z-index for buttons */
  display: flex !important;
  visibility: visible !important;
  background-color: #000000 !important; /* Force black background */
  color: #ffffff !important; /* Force white text */
  border: 1px solid #374151 !important; /* Force border */
}

.project-card-button:hover {
  background-color: #1f2937 !important; /* Force hover state */
}

@media (max-width: 640px) {
  .project-card-button {
    @apply py-3 px-4 text-base;
    min-height: 48px; /* Larger touch target on mobile */
  }

  /* Stack buttons vertically on mobile */
  .project-card-actions .flex {
    @apply flex-col gap-2;
  }
}

/* ===== RESPONSIVE GRID SYSTEM ===== */

/* Unified responsive grid for project cards */
.projects-grid-unified {
  @apply grid;
  gap: var(--card-gap-mobile);
  grid-template-columns: 1fr; /* Mobile: single column */
  align-items: start; /* Prevent stretching and overlapping */
  overflow: visible; /* Ensure no clipping of buttons */
}

.projects-grid-unified > * {
  overflow: visible; /* Ensure child cards don't clip buttons */
}

/* Tablet: 2 columns */
@media (min-width: 640px) {
  .projects-grid-unified {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--card-gap-tablet);
  }
}

/* Desktop: 3 columns */
@media (min-width: 1024px) {
  .projects-grid-unified {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--card-gap-desktop);
  }
}

/* Large desktop: 4 columns */
@media (min-width: 1280px) {
  .projects-grid-unified {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Container padding for different screen sizes */
.projects-container {
  @apply max-w-7xl mx-auto;
  padding: 0 var(--mobile-padding);
}

@media (min-width: 640px) {
  .projects-container {
    padding: 0 var(--tablet-padding);
  }
}

@media (min-width: 1024px) {
  .projects-container {
    padding: 0 var(--desktop-padding);
  }
}

/* Project card container to prevent overlapping */
.project-card-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 280px;
}

@media (max-width: 640px) {
  .project-card-container {
    min-height: 260px;
  }
}

/* Loading skeleton responsive - matches new card design */
.project-card-skeleton {
  @apply bg-gray-800 rounded-lg animate-pulse;
  aspect-ratio: 4/3;
  height: 260px;
}

@media (min-width: 640px) {
  .project-card-skeleton {
    height: 280px;
  }
}

/* ===== MOBILE NAVIGATION ENHANCEMENTS ===== */

/* Mobile menu improvements */
@media (max-width: 768px) {
  .mobile-menu-item {
    @apply block px-4 py-3 rounded-lg text-base font-medium;
    min-height: var(--mobile-min-touch-target);
  }

  .mobile-menu-button {
    @apply p-3 rounded-lg;
    min-height: var(--mobile-min-touch-target);
    min-width: var(--mobile-min-touch-target);
  }
}

/* ===== MOBILE OAUTH & AUTHENTICATION ===== */

/* OAuth button enhancements for mobile */
.oauth-button-mobile {
  @apply w-full py-4 px-6 text-base font-medium rounded-lg transition-all duration-200;
  min-height: var(--mobile-min-touch-target);
  touch-action: manipulation; /* Prevents zoom on double-tap */
}

@media (max-width: 640px) {
  .oauth-button-mobile {
    @apply py-5 px-8 text-lg;
    min-height: 56px; /* Larger touch target on mobile */
  }
}

/* Login form mobile optimizations */
@media (max-width: 640px) {
  .login-form-container {
    @apply px-4 py-6;
    min-height: calc(100vh - 4rem); /* Account for navbar */
  }

  .login-form-card {
    @apply p-6 mx-2;
    max-width: calc(100vw - 2rem);
  }

  .login-input-mobile {
    @apply py-4 px-4 text-base;
    min-height: var(--mobile-min-touch-target);
  }

  .login-button-mobile {
    @apply py-4 px-6 text-base font-semibold;
    min-height: var(--mobile-min-touch-target);
  }
}

/* Mobile viewport fixes for authentication */
@media (max-width: 640px) {
  .auth-page-mobile {
    min-height: 100vh;
    min-height: -webkit-fill-available; /* iOS Safari fix */
  }

  .auth-content-mobile {
    padding-bottom: env(safe-area-inset-bottom); /* iOS safe area */
  }
}

/* Touch-friendly form elements */
@media (max-width: 768px) {
  input[type="email"],
  input[type="password"],
  input[type="text"],
  input[type="tel"],
  input[type="number"],
  textarea,
  select {
    font-size: 16px !important; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    border-radius: 8px;
    min-height: var(--mobile-min-touch-target);
    padding: 12px 16px;
  }

  button,
  .btn,
  [role="button"] {
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight */
    touch-action: manipulation;
    min-height: var(--mobile-min-touch-target);
    min-width: var(--mobile-min-touch-target);
  }

  /* Ensure clickable elements are large enough */
  a, button, input, select, textarea {
    min-height: 44px;
  }
}

/* ===== COMPREHENSIVE MOBILE OPTIMIZATIONS ===== */

/* Mobile-first typography scaling */
@media (max-width: 640px) {
  h1 {
    font-size: 1.875rem !important; /* 30px */
    line-height: 1.2;
  }

  h2 {
    font-size: 1.5rem !important; /* 24px */
    line-height: 1.3;
  }

  h3 {
    font-size: 1.25rem !important; /* 20px */
    line-height: 1.4;
  }

  h4 {
    font-size: 1.125rem !important; /* 18px */
    line-height: 1.4;
  }

  /* Improve text readability on mobile */
  p, span, div {
    line-height: 1.6;
  }
}

/* Mobile container improvements */
@media (max-width: 640px) {
  .container,
  .max-w-7xl,
  .max-w-6xl,
  .max-w-5xl,
  .max-w-4xl {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    max-width: 100% !important;
  }

  /* Ensure full width on mobile */
  .w-full {
    width: 100% !important;
  }
}

/* Mobile modal improvements */
@media (max-width: 640px) {
  .modal-detail-backdrop,
  .modal-image-backdrop {
    padding: 0.5rem !important;
  }

  .modal-detail-backdrop > div,
  .modal-image-backdrop > div {
    margin: 0 !important;
    max-width: 100% !important;
    max-height: 100vh !important;
    border-radius: 0.5rem !important;
  }
}

/* Mobile table responsiveness */
@media (max-width: 640px) {
  table {
    font-size: 0.875rem;
  }

  th, td {
    padding: 0.5rem !important;
  }

  /* Stack table cells on very small screens */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
  .navbar-enhanced {
    padding: 0.75rem 1rem !important;
  }

  .navbar-brand {
    font-size: 1.25rem !important;
  }

  .navbar-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 50;
  }
}

/* Mobile dashboard improvements */
@media (max-width: 640px) {
  .dashboard-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .dashboard-card {
    padding: 1rem !important;
  }

  .dashboard-stats {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }
}

/* Mobile form improvements */
@media (max-width: 640px) {
  .form-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  .form-actions {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .form-actions button {
    width: 100% !important;
  }
}

/* Mobile image gallery improvements */
@media (max-width: 640px) {
  .image-gallery {
    grid-template-columns: 1fr !important;
  }

  .image-modal {
    padding: 0.5rem !important;
  }

  .image-modal img {
    max-height: 70vh !important;
    width: 100% !important;
    object-fit: contain;
  }
}

/* Mobile footer improvements */
@media (max-width: 640px) {
  .footer-grid {
    grid-template-columns: 1fr !important;
    text-align: center !important;
    gap: 2rem !important;
  }

  .footer-links {
    justify-content: center !important;
  }
}

/* ===== MOBILE PERFORMANCE & ACCESSIBILITY ===== */

/* Improve mobile scrolling performance */
@media (max-width: 768px) {
  * {
    -webkit-overflow-scrolling: touch;
  }

  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Prevent horizontal scroll on mobile */
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Improve tap targets */
  .btn, button, a, input, select, textarea {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Mobile-friendly focus states */
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

/* Mobile landscape optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .navbar-enhanced {
    padding: 0.5rem 1rem !important;
  }

  .page-with-navbar {
    padding-top: 3.5rem !important;
  }
}

/* Very small screens (320px) */
@media (max-width: 320px) {
  .container,
  .projects-container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  h1 {
    font-size: 1.5rem !important;
  }

  h2 {
    font-size: 1.25rem !important;
  }

  .project-card-unified {
    height: 240px !important;
  }
}

/* Clean project detail modal styling */
.fixed.inset-0.z-50 .whitespace-pre-wrap {
  color: #e5e7eb;
  background-color: transparent;
}

.fixed.inset-0.z-50 .bg-white,
.fixed.inset-0.z-50 .bg-gray-50,
.fixed.inset-0.z-50 .bg-gray-100 {
  background-color: #111827;
  color: #e5e7eb;
}

/* Removed excessive CSS for better performance and fewer conflicts */

/* Essential badge styling for project detail modals */
.fixed.inset-0.z-50 .px-2.py-1.text-xs.font-medium.rounded {
  opacity: 1;
  visibility: visible;
}

/* Admin theme wrapper - dark black theme for admin pages */
.admin-theme-wrapper {
  --background: 0 0 0; /* black */
  --foreground: 255 255 255; /* white */
  --card: 17 17 17; /* #111111 */
  --card-foreground: 255 255 255; /* white */
  --popover: 17 17 17; /* #111111 */
  --popover-foreground: 255 255 255; /* white */
  --primary: 59 130 246; /* blue-500 */
  --primary-foreground: 255 255 255; /* white */
  --secondary: 31 31 31; /* #1f1f1f */
  --secondary-foreground: 255 255 255; /* white */
  --muted: 31 31 31; /* #1f1f1f */
  --muted-foreground: 156 163 175; /* gray-400 */
  --accent: 31 31 31; /* #1f1f1f */
  --accent-foreground: 255 255 255; /* white */
  --destructive: 239 68 68; /* red-500 */
  --destructive-foreground: 255 255 255; /* white */
  --border: 55 55 55; /* #373737 */
  --input: 55 55 55; /* #373737 */
  --ring: 59 130 246; /* blue-500 */
}

.admin-theme-wrapper * {
  color-scheme: dark;
}
